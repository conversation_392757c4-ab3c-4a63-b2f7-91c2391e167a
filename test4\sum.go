/*
可变参数函数
可变参数优点：
1. 语法简洁
2. 无需手动创建切片
3. 编译器优化小参数
可变参数缺点：
1. 每次调用都创建新切片
2. 无法重用已有切片
*/
package main

import "fmt"

func sum(nums ...int) int {
	sum := 0
	for _, num := range nums {
		sum += num
	}
	return sum
}

func main() {
	result := sum(1, 2, 3, 4, 5)
	fmt.Println(result)
	fmt.Println(max(1, 2, 3, 4, 5))
	fmt.Println(min(1, 2, 3, 4, 5))
	
}

func max(nums ...int) int {
	max := nums[0]
	for _, num := range nums {
		if num > max {
			max = num
		}
	}
	return max
}

func min(nums ...int) int {
	min := nums[0]
	for _, num := range nums {
		if num < min {
			min = num
		}
	}
	return min
}
