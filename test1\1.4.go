// Lissajous 生成随机利萨茹曲线的 GIF 动画
package main

import (
	"fmt"
	"image"
	"image/color"
	"image/gif"
	"io"
	"math"
	"math/rand"
	"os"
	"time"
)

// 调色板：白色和自定义RGBA颜色
var palette = []color.Color{color.White, color.RGBA{R: 255, G: 126, B: 255, A: 255}}

const (
	whiteIndex = 0 // 调色板中的第一个颜色（白色）
	rgbaIndex  = 1 // 调色板中的第二个颜色（紫色）
)

func test1_4() {
	// 图像序列是确定性的，除非我们使用当前时间为伪随机数生成器设置种子
	// 感谢 Randall McPherson 指出了这个遗漏
	// 注意：rand.Seed 在 Go 1.20 中已被弃用，但为了兼容性保留
	rand.Seed(time.Now().UTC().UnixNano())

	// 创建输出文件
	file, err := os.Create("lissajous.gif")
	if err != nil {
		fmt.Fprintf(os.Stderr, "创建文件失败: %v\n", err)
		os.Exit(1)
	}
	defer file.Close() // 确保函数结束时关闭文件

	lissajous(file)                         // 生成利萨茹曲线动画
	fmt.Println("GIF 动画已保存到 lissajous.gif") // 提示用户文件已保存
}

// lissajous 生成利萨茹曲线的 GIF 动画
func lissajous(out io.Writer) {
	const (
		cycles  = 5     // x 振荡器的完整旋转次数
		res     = 0.001 // 角度分辨率
		size    = 100   // 图像画布覆盖范围 [-size..+size]
		nframes = 64    // 动画帧数
		delay   = 8     // 帧间延迟，单位为 10ms
	)

	freq := rand.Float64() * 3.0        // y 振荡器的相对频率（随机）
	anim := gif.GIF{LoopCount: nframes} // GIF 动画结构
	phase := 0.0                        // 相位差

	// 生成每一帧
	for i := 0; i < nframes; i++ {
		rect := image.Rect(0, 0, 2*size+1, 2*size+1) // 创建矩形画布
		img := image.NewPaletted(rect, palette)      // 创建调色板图像

		// 绘制利萨茹曲线
		for t := 0.0; t < cycles*2*math.Pi; t += res {
			x := math.Sin(t)              // x 坐标：sin(t)
			y := math.Sin(t*freq + phase) // y 坐标：sin(t*freq + phase)
			// 将数学坐标转换为图像坐标并设置像素颜色
			img.SetColorIndex(size+int(x*size+0.5), size+int(y*size+0.5), rgbaIndex)
		}

		phase += 0.1                           // 每帧增加相位差，产生动画效果
		anim.Delay = append(anim.Delay, delay) // 添加帧延迟
		anim.Image = append(anim.Image, img)   // 添加图像帧
	}

	gif.EncodeAll(out, &anim) // 将动画编码为 GIF 格式（忽略编码错误）
}
