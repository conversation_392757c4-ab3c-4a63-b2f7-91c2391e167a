// 演示切片操作和 appendInt 函数
package main

import "fmt"

func main() {
	fmt.Println("=== 切片操作详解 ===")
	
	// 演示 z = x[:zlen] 的含义
	demonstrateSlicing()
	
	// 演示 appendInt 函数
	demonstrateAppendInt()
}

func demonstrateSlicing() {
	fmt.Println("\n1. 切片操作 x[:zlen] 详解:")
	
	// 创建一个有容量的切片
	x := make([]int, 3, 6)  // 长度3，容量6
	x[0], x[1], x[2] = 10, 20, 30
	
	fmt.Printf("原始切片 x: %v\n", x)
	fmt.Printf("x 的长度: %d, 容量: %d\n", len(x), cap(x))
	fmt.Printf("x 的底层数组地址: %p\n", &x[0])
	
	// 模拟 appendInt 中的操作
	zlen := len(x) + 1  // 4
	fmt.Printf("新长度 zlen: %d\n", zlen)
	
	if zlen <= cap(x) {
		fmt.Println("容量足够，使用 z = x[:zlen]")
		z := x[:zlen]  // 扩展切片到长度4
		
		fmt.Printf("新切片 z: %v\n", z)
		fmt.Printf("z 的长度: %d, 容量: %d\n", len(z), cap(z))
		fmt.Printf("z 的底层数组地址: %p\n", &z[0])
		
		// 关键观察：z 和 x 共享底层数组
		fmt.Printf("z 和 x 共享底层数组: %t\n", &x[0] == &z[0])
		
		// 修改 z 会影响 x
		z[3] = 40  // 在新位置添加元素
		fmt.Printf("添加元素后 z: %v\n", z)
		
		// 查看原始数组的变化
		fullArray := x[:cap(x)]  // 查看完整的底层数组
		fmt.Printf("底层数组内容: %v\n", fullArray)
	}
}

// 你提供的 appendInt 函数
func appendInt(x []int, y int) []int {
	var z []int
	zlen := len(x) + 1
	if zlen <= cap(x) {
		// There is room to grow. Extend the slice.
		z = x[:zlen]  // 关键操作：扩展切片
	} else {
		// There is insufficient space. Allocate a new array.
		// Grow by doubling, for amortized linear complexity.
		zcap := zlen
		if zcap < 2*len(x) {
			zcap = 2 * len(x)
		}
		z = make([]int, zlen, zcap)
		copy(z, x) // a built-in function; see text
	}
	z[len(x)] = y
	return z
}

func demonstrateAppendInt() {
	fmt.Println("\n=== appendInt 函数演示 ===")
	
	// 情况1：容量足够
	fmt.Println("\n情况1：容量足够时")
	x1 := make([]int, 2, 5)  // 长度2，容量5
	x1[0], x1[1] = 100, 200
	
	fmt.Printf("原始切片: %v (长度:%d, 容量:%d)\n", x1, len(x1), cap(x1))
	
	result1 := appendInt(x1, 300)
	fmt.Printf("添加后: %v (长度:%d, 容量:%d)\n", result1, len(result1), cap(result1))
	fmt.Printf("共享底层数组: %t\n", &x1[0] == &result1[0])
	
	// 情况2：容量不足
	fmt.Println("\n情况2：容量不足时")
	x2 := []int{1, 2, 3}  // 长度和容量都是3
	fmt.Printf("原始切片: %v (长度:%d, 容量:%d)\n", x2, len(x2), cap(x2))
	
	result2 := appendInt(x2, 4)
	fmt.Printf("添加后: %v (长度:%d, 容量:%d)\n", result2, len(result2), cap(result2))
	fmt.Printf("共享底层数组: %t\n", len(x2) > 0 && len(result2) > 0 && &x2[0] == &result2[0])
	
	// 演示切片语法
	fmt.Println("\n=== 切片语法对比 ===")
	arr := []int{10, 20, 30, 40, 50}
	fmt.Printf("原数组: %v\n", arr)
	
	slice1 := arr[:3]    // 前3个元素 [10, 20, 30]
	slice2 := arr[1:4]   // 索引1到3 [20, 30, 40]
	slice3 := arr[2:]    // 从索引2到末尾 [30, 40, 50]
	
	fmt.Printf("arr[:3]:  %v\n", slice1)
	fmt.Printf("arr[1:4]: %v\n", slice2)
	fmt.Printf("arr[2:]:  %v\n", slice3)
}
