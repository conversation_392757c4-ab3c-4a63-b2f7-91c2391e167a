package main

import (
	"fmt"
	"os"
)

func test1_2() {
	//fmt.Println(os.Args[0])
	var s, sep string
	for _, arg := range os.Args[0:] {
		s += sep + arg
		sep = " "
	}
	fmt.Println(s)

	h := "hello"

	fmt.Println("=== range中的变量名可以随意命名 ===")

	// 1. 使用不同的变量名
	fmt.Println("1. 用char作为变量名:")
	for _, char := range h {
		fmt.Printf("%c ", char)
	}

	fmt.Println("\n\n2. 用c作为变量名:")
	for _, c := range h {
		fmt.Printf("%c ", c)
	}

	fmt.Println("\n\n3. 用字符作为变量名:")
	for _, 字符 := range h {
		fmt.Printf("%c ", 字符)
	}

	fmt.Println("\n\n4. 用letter作为变量名:")
	for _, letter := range h {
		fmt.Printf("%c ", letter)
	}

	fmt.Println("\n\n5. 用任意名字x作为变量名:")
	for _, x := range h {
		fmt.Printf("%c ", x)
	}

	fmt.Println("\n\n=== range语法解释 ===")
	fmt.Println("for 索引变量, 值变量 := range 字符串 {}")
	fmt.Println("- 第一个变量：字符在字符串中的字节位置")
	fmt.Println("- 第二个变量：字符的Unicode值(rune类型)")
	fmt.Println("- 如果不需要索引，用 _ 忽略")
	fmt.Println("- 变量名可以是任何合法的Go标识符")

	fmt.Println("\n=== 同时获取索引和字符 ===")
	for index, character := range h {
		fmt.Printf("位置%d: %c\n", index, character)
	}

}
