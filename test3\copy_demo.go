// 演示 copy 函数的使用
package main

import "fmt"

func main() {
	fmt.Println("=== copy 函数详解 ===")

	// 基本用法
	basicCopy()

	// 复制规则
	copyRules()

	// 特殊情况
	specialCases()

	// 实际应用
	practicalUsage()
}

func basicCopy() {
	fmt.Println("\n1. 基本用法:")

	// 创建源切片和目标切片
	src := []int{1, 2, 3, 4, 5}
	dst := make([]int, 3) // 长度为3的切片

	fmt.Printf("源切片: %v\n", src)
	fmt.Printf("目标切片(复制前): %v\n", dst)

	// 执行复制
	n := copy(dst, src)

	fmt.Printf("目标切片(复制后): %v\n", dst)
	fmt.Printf("实际复制了 %d 个元素\n", n)

	// 修改源切片，验证是否独立
	src[0] = 999
	fmt.Printf("修改源切片后: src=%v, dst=%v\n", src, dst)
}

func copyRules() {
	fmt.Println("\n2. 复制规则:")

	// 规则1：复制个数 = min(len(dst), len(src))
	fmt.Println("规则1: 复制个数取决于较短的切片")

	src1 := []int{1, 2, 3, 4, 5}
	dst1 := make([]int, 3) // 目标更短
	n1 := copy(dst1, src1)
	fmt.Printf("src长度5, dst长度3 → 复制%d个: %v\n", n1, dst1)

	src2 := []int{1, 2}
	dst2 := make([]int, 5) // 源更短
	n2 := copy(dst2, src2)
	fmt.Printf("src长度2, dst长度5 → 复制%d个: %v\n", n2, dst2)

	// 规则2：可以部分复制
	fmt.Println("\n规则2: 可以复制切片的一部分")
	src3 := []int{10, 20, 30, 40, 50}
	dst3 := make([]int, 3)

	n3 := copy(dst3, src3[1:4]) // 复制src3的索引1-3
	fmt.Printf("复制 src[1:4] → %v (复制了%d个)\n", dst3, n3)
}

func specialCases() {
	fmt.Println("\n3. 特殊情况:")

	// 情况1：自己复制给自己
	fmt.Println("情况1: 切片自己复制给自己")
	data := []int{1, 2, 3, 4, 5}
	fmt.Printf("原始数据: %v\n", data)

	n := copy(data[2:], data[:3]) // 将前3个元素复制到从索引2开始的位置
	fmt.Printf("copy(data[2:], data[:3]) → %v (复制了%d个)\n", data, n)

	// 情况2：字符串到字节切片
	fmt.Println("\n情况2: 字符串复制到字节切片")
	str := "Hello, 世界"
	bytes := make([]byte, 20)

	n2 := copy(bytes, str) // 字符串可以直接复制到[]byte
	fmt.Printf("字符串: %s\n", str)
	fmt.Printf("复制到字节切片: %v\n", bytes[:n2])
	fmt.Printf("转回字符串: %s\n", string(bytes[:n2]))

	// 情况3：空切片
	fmt.Println("\n情况3: 空切片")
	var empty []int
	target := make([]int, 3)

	n3 := copy(target, empty)
	fmt.Printf("从空切片复制: %v (复制了%d个)\n", target, n3)
}

func practicalUsage() {
	fmt.Println("\n4. 实际应用:")

	// 应用1：安全复制切片
	fmt.Println("应用1: 创建切片的独立副本")
	original := []int{1, 2, 3, 4, 5}
	backup := make([]int, len(original))
	copy(backup, original)

	fmt.Printf("原始: %v\n", original)
	fmt.Printf("备份: %v\n", backup)

	original[0] = 999
	fmt.Printf("修改原始后: 原始=%v, 备份=%v\n", original, backup)

	// 应用2：在appendInt中的使用
	fmt.Println("\n应用2: appendInt函数中的copy")
	demonstrateAppendIntCopy()

	// 应用3：移除切片元素
	fmt.Println("\n应用3: 移除切片中的元素")
	nums := []int{1, 2, 3, 4, 5}
	fmt.Printf("原始切片: %v\n", nums)

	// 移除索引2的元素(值为3)
	index := 2
	copy(nums[index:], nums[index+1:]) // 将后面的元素前移
	nums = nums[:len(nums)-1]          // 缩短切片

	fmt.Printf("移除索引2后: %v\n", nums)
}

func demonstrateAppendIntCopy() {
	// 模拟appendInt中容量不足时的情况
	x := []int{1, 2, 3} // 容量刚好等于长度
	y := 4

	fmt.Printf("原切片: %v (长度:%d, 容量:%d)\n", x, len(x), cap(x))

	// 模拟appendInt的逻辑
	zlen := len(x) + 1
	zcap := 2 * len(x) // 容量翻倍
	z := make([]int, zlen, zcap)

	fmt.Printf("新切片(复制前): %v (长度:%d, 容量:%d)\n", z, len(z), cap(z))

	// 关键：使用copy复制原有元素
	copy(z, x)
	z[len(x)] = y

	fmt.Printf("新切片(复制后): %v (长度:%d, 容量:%d)\n", z, len(z), cap(z))
	fmt.Printf("原切片地址: %p, 新切片地址: %p\n", &x[0], &z[0])
}
