// Package tempconv performs Celsius, Fahrenheit, and Kelvin conversions.
package tempconv

// CToF converts a Celsius temperature to Fahrenheit.
func CToF(c Celsius) Fahrenheit { return Fahrenheit(c*9/5 + 32) }

// FToC converts a Fahrenheit temperature to Celsius.
func FToC(f Fahrenheit) Celsius { return Celsius((f - 32) * 5 / 9) }

// CtoK converts a Celsius temperature to Kelvin.
func CtoK(c Celsius) Kelvin { return Kelvin(c + 273.15) }

// KtoC converts a Kelvin temperature to Celsius.
func KtoC(k Kelvin) Celsius { return Celsius(k - 273.15) }

// FtoK converts a Fahrenheit temperature to Kelvin.
func FtoK(f Fahrenheit) Kelvin { return CtoK(FToC(f)) }

// KtoF converts a Kelvin temperature to Fahrenheit.
func KtoF(k Kelvin) Fahrenheit { return CToF(KtoC(k)) }
