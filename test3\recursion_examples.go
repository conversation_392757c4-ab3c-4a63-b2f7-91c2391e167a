/*
递归示例
递归实际上就是函数自己调用自己，每次调用时问题的规模都会减小，直到达到基本情况为止。
递归的适应场景包括树形结构的遍历、分治算法等。
*/
package main

import "fmt"

// 1. 阶乘计算 - 经典递归示例
func factorial(n int) int {
	// 基础情况：0! = 1, 1! = 1
	if n <= 1 {
		return 1
	}
	// 递归情况：n! = n * (n-1)!
	return n * factorial(n-1)
}

// 2. 斐波那契数列
func fibonacci(n int) int {
	// 基础情况
	if n <= 1 {
		return n
	}
	// 递归情况：F(n) = F(n-1) + F(n-2)
	return fibonacci(n-1) + fi<PERSON><PERSON><PERSON>(n-2)
}

// 3. 二分查找 - 递归版本
func binarySearch(arr []int, target, left, right int) int {
	// 基础情况：未找到
	if left > right {
		return -1
	}

	mid := left + (right-left)/2

	// 基础情况：找到目标
	if arr[mid] == target {
		return mid
	}

	// 递归情况
	if arr[mid] > target {
		return binarySearch(arr, target, left, mid-1)
	} else {
		return binarySearch(arr, target, mid+1, right)
	}
}

// 4. 计算数组元素之和
func sumArray(arr []int) int {
	// 基础情况：空数组
	if len(arr) == 0 {
		return 0
	}
	// 基础情况：只有一个元素
	if len(arr) == 1 {
		return arr[0]
	}
	// 递归情况：第一个元素 + 剩余元素的和
	return arr[0] + sumArray(arr[1:])
}

// 5. 字符串反转
func reverseString(s string) string {
	// 基础情况：空字符串或单字符
	if len(s) <= 1 {
		return s
	}
	// 递归情况：最后一个字符 + 反转剩余字符串
	return string(s[len(s)-1]) + reverseString(s[:len(s)-1])
}

// 6. 计算x的n次幂
func power(x, n int) int {
	// 基础情况
	if n == 0 {
		return 1
	}
	if n == 1 {
		return x
	}

	// 递归情况：优化版本（快速幂）
	if n%2 == 0 {
		half := power(x, n/2)
		return half * half
	} else {
		return x * power(x, n-1)
	}
}

// 7. 树形结构遍历（类似你的HTML遍历）
type TreeNode struct {
	Value    int
	Children []*TreeNode
}

func traverseTree(node *TreeNode, depth int) {
	if node == nil {
		return
	}

	// 打印当前节点（带缩进显示层级）
	for i := 0; i < depth; i++ {
		fmt.Print("  ")
	}
	fmt.Printf("Node: %d\n", node.Value)

	// 递归遍历所有子节点
	for _, child := range node.Children {
		traverseTree(child, depth+1)
	}
}

// 8. 目录遍历示例（概念性，不实际访问文件系统）
func printDirectoryStructure(path string, depth int) {
	// 基础情况：最大深度限制
	if depth > 5 {
		return
	}

	// 打印当前路径
	for i := 0; i < depth; i++ {
		fmt.Print("  ")
	}
	fmt.Printf("📁 %s\n", path)

	// 模拟子目录（实际应用中会读取真实目录）
	subdirs := []string{"subdir1", "subdir2"}
	for _, subdir := range subdirs {
		if depth < 2 { // 限制递归深度避免无限递归
			printDirectoryStructure(path+"/"+subdir, depth+1)
		}
	}
}

// 可变参数示例 - 内存优化分析
func sumVariadic(nums ...int) int {
	// nums 是一个 []int 切片
	sum := 0
	for _, num := range nums {
		sum += num
	}
	return sum
}

// 切片参数版本
func sumSlice(nums []int) int {
	sum := 0
	for _, num := range nums {
		sum += num
	}
	return sum
}

// 递归版本的可变参数求和
func sumRecursiveVariadic(nums ...int) int {
	if len(nums) == 0 {
		return 0
	}
	if len(nums) == 1 {
		return nums[0]
	}
	return nums[0] + sumRecursiveVariadic(nums[1:]...)
}

// 内存使用分析函数
func analyzeMemoryUsage() {
	fmt.Println("\n=== 可变参数内存分析 ===")

	// 1. 直接传递参数 vs 创建切片
	fmt.Println("1. 调用方式对比:")

	// 可变参数：编译器自动创建切片
	result1 := sumVariadic(1, 2, 3, 4, 5)
	fmt.Printf("可变参数调用: sumVariadic(1,2,3,4,5) = %d\n", result1)

	// 切片参数：需要手动创建切片
	nums := []int{1, 2, 3, 4, 5}
	result2 := sumSlice(nums)
	fmt.Printf("切片参数调用: sumSlice([]int{1,2,3,4,5}) = %d\n", result2)

	// 2. 内存分配分析
	fmt.Println("\n2. 内存分配分析:")
	fmt.Println("可变参数优势:")
	fmt.Println("  - 编译器优化：小参数可能在栈上分配")
	fmt.Println("  - 避免显式切片创建")
	fmt.Println("  - 调用语法简洁")

	fmt.Println("可变参数劣势:")
	fmt.Println("  - 每次调用都创建新切片")
	fmt.Println("  - 无法重用已有切片")

	// 3. 性能对比场景
	fmt.Println("\n3. 不同场景的选择:")

	// 场景1：少量参数，频繁调用
	fmt.Println("场景1 - 少量参数:")
	fmt.Printf("  sumVariadic(1, 2, 3) = %d (推荐)\n", sumVariadic(1, 2, 3))

	// 场景2：大量数据，已有切片
	largeSlice := make([]int, 1000)
	for i := range largeSlice {
		largeSlice[i] = i + 1
	}
	fmt.Printf("  sumSlice(大切片) = %d (推荐)\n", sumSlice(largeSlice))

	// 场景3：可变参数展开已有切片
	fmt.Printf("  sumVariadic(切片展开...) = %d\n", sumVariadic(nums...))
}

func main() {
	fmt.Println("=== 递归示例演示 ===\n")

	// 1. 阶乘
	fmt.Printf("5! = %d\n", factorial(5))

	// 2. 斐波那契
	fmt.Printf("第7个斐波那契数: %d\n", fibonacci(7))

	// 3. 二分查找
	arr := []int{1, 3, 5, 7, 9, 11, 13}
	index := binarySearch(arr, 7, 0, len(arr)-1)
	fmt.Printf("在数组中查找7的位置: %d\n", index)

	// 4. 数组求和 - 对比不同实现
	nums := []int{1, 2, 3, 4, 5}
	fmt.Printf("递归求和: %v = %d\n", nums, sumArray(nums))
	fmt.Printf("可变参数求和: %d\n", sumVariadic(1, 2, 3, 4, 5))
	fmt.Printf("递归可变参数: %d\n", sumRecursiveVariadic(1, 2, 3, 4, 5))

	// 5. 字符串反转
	str := "Hello"
	fmt.Printf("'%s' 反转后: '%s'\n", str, reverseString(str))

	// 6. 幂运算
	fmt.Printf("2^8 = %d\n", power(2, 8))

	// 7. 树遍历
	fmt.Println("\n树结构遍历:")
	root := &TreeNode{
		Value: 1,
		Children: []*TreeNode{
			{Value: 2, Children: []*TreeNode{{Value: 4}, {Value: 5}}},
			{Value: 3, Children: []*TreeNode{{Value: 6}}},
		},
	}
	traverseTree(root, 0)

	// 8. 目录结构
	fmt.Println("\n目录结构示例:")
	printDirectoryStructure("root", 0)

	// 9. 可变参数内存分析
	analyzeMemoryUsage()
}
