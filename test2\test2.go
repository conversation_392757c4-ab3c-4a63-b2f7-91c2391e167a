package main

import (
	"fmt"
	"test2/tempconv" // 导入本地的tempconv包
)

func main() {
	fmt.Println("=== 温度转换演示 ===")

	// 使用tempconv包的类型和函数
	c := tempconv.FToC(212) // 212华氏度转摄氏度
	fmt.Printf("212°F = %v\n", c)

	f := tempconv.CToF(100) // 100摄氏度转华氏度
	fmt.Printf("100°C = %v\n", f)

	k := tempconv.CtoK(100) // 100摄氏度转开尔文
	fmt.Printf("100°C = %v\n", k)

	// 使用摄氏度常量
	fmt.Printf("摄氏度 - 绝对零度: %v\n", tempconv.AbsoluteZeroC)
	fmt.Printf("摄氏度 - 冰点: %v\n", tempconv.FreezingC)
	fmt.Printf("摄氏度 - 沸点: %v\n", tempconv.BoilingC)

	// 使用开尔文常量
	fmt.Printf("开尔文 - 绝对零度: %v\n", tempconv.AbsoluteZeroK)
	fmt.Printf("开尔文 - 冰点: %v\n", tempconv.FreezingK)
	fmt.Printf("开尔文 - 沸点: %v\n", tempconv.BoilingK)

	// 演示类型区分
	fmt.Println("\n=== 类型区分演示 ===")
	var celsius tempconv.Celsius = 25
	var kelvin tempconv.Kelvin = 298.15

	fmt.Printf("摄氏度变量: %v (类型: %T)\n", celsius, celsius)
	fmt.Printf("开尔文变量: %v (类型: %T)\n", kelvin, kelvin)

	// 不能直接比较不同类型
	// fmt.Println(celsius == kelvin)  // ❌ 编译错误！

	// 需要转换后比较
	fmt.Printf("25°C 转换为开尔文: %v\n", tempconv.CtoK(celsius))
	fmt.Printf("298.15K 转换为摄氏度: %v\n", tempconv.KtoC(kelvin))
}
