// 详细的HTTP服务器示例
package main

import (
	"fmt"
	"log"
	"net/http"
	"time"
)

func main() {
	// 注册多个路由处理器
	http.HandleFunc("/", rootHandler)           // 根路径
	http.HandleFunc("/hello", helloHandler)     // /hello 路径
	http.HandleFunc("/time", timeHandler)       // /time 路径
	http.HandleFunc("/info", infoHandler)       // /info 路径
	
	fmt.Println("服务器启动在 http://localhost:8001")
	fmt.Println("可访问的路径:")
	fmt.Println("  http://localhost:8001/")
	fmt.Println("  http://localhost:8001/hello")
	fmt.Println("  http://localhost:8001/time")
	fmt.Println("  http://localhost:8001/info")
	
	// 启动服务器
	log.Fatal(http.ListenAndServe("localhost:8001", nil))
}

// 根路径处理器
func rootHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("收到请求: %s %s\n", r.Method, r.URL.Path)
	fmt.Fprintf(w, "欢迎访问根路径！\n")
	fmt.Fprintf(w, "请求路径: %q\n", r.URL.Path)
	fmt.Fprintf(w, "请求方法: %s\n", r.Method)
}

// Hello 处理器
func helloHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("收到Hello请求: %s\n", r.URL.Path)
	fmt.Fprintf(w, "Hello, World!\n")
	fmt.Fprintf(w, "当前时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
}

// 时间处理器
func timeHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("收到时间请求: %s\n", r.URL.Path)
	now := time.Now()
	fmt.Fprintf(w, "当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Fprintf(w, "Unix时间戳: %d\n", now.Unix())
}

// 信息处理器
func infoHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("收到信息请求: %s\n", r.URL.Path)
	fmt.Fprintf(w, "=== 请求信息 ===\n")
	fmt.Fprintf(w, "URL路径: %q\n", r.URL.Path)
	fmt.Fprintf(w, "请求方法: %s\n", r.Method)
	fmt.Fprintf(w, "用户代理: %s\n", r.UserAgent())
	fmt.Fprintf(w, "远程地址: %s\n", r.RemoteAddr)
}
