package main

import "fmt"

// 1. 阶乘计算 - 经典递归示例
func factorial(n int) int {
    // 基础情况：0! = 1, 1! = 1
    if n <= 1 {
        return 1
    }
    // 递归情况：n! = n * (n-1)!
    return n * factorial(n-1)
}

// 2. 斐波那契数列
func fibonacci(n int) int {
    // 基础情况
    if n <= 1 {
        return n
    }
    // 递归情况：F(n) = F(n-1) + F(n-2)
    return fibonacci(n-1) + fi<PERSON><PERSON><PERSON>(n-2)
}

// 3. 二分查找 - 递归版本
func binarySearch(arr []int, target, left, right int) int {
    // 基础情况：未找到
    if left > right {
        return -1
    }
    
    mid := left + (right-left)/2
    
    // 基础情况：找到目标
    if arr[mid] == target {
        return mid
    }
    
    // 递归情况
    if arr[mid] > target {
        return binarySearch(arr, target, left, mid-1)
    } else {
        return binarySearch(arr, target, mid+1, right)
    }
}

// 4. 计算数组元素之和
func sumArray(arr []int) int {
    // 基础情况：空数组
    if len(arr) == 0 {
        return 0
    }
    // 基础情况：只有一个元素
    if len(arr) == 1 {
        return arr[0]
    }
    // 递归情况：第一个元素 + 剩余元素的和
    return arr[0] + sumArray(arr[1:])
}

// 5. 字符串反转
func reverseString(s string) string {
    // 基础情况：空字符串或单字符
    if len(s) <= 1 {
        return s
    }
    // 递归情况：最后一个字符 + 反转剩余字符串
    return string(s[len(s)-1]) + reverseString(s[:len(s)-1])
}

// 6. 计算x的n次幂
func power(x, n int) int {
    // 基础情况
    if n == 0 {
        return 1
    }
    if n == 1 {
        return x
    }
    
    // 递归情况：优化版本（快速幂）
    if n%2 == 0 {
        half := power(x, n/2)
        return half * half
    } else {
        return x * power(x, n-1)
    }
}

// 7. 树形结构遍历（类似你的HTML遍历）
type TreeNode struct {
    Value    int
    Children []*TreeNode
}

func traverseTree(node *TreeNode, depth int) {
    if node == nil {
        return
    }
    
    // 打印当前节点（带缩进显示层级）
    for i := 0; i < depth; i++ {
        fmt.Print("  ")
    }
    fmt.Printf("Node: %d\n", node.Value)
    
    // 递归遍历所有子节点
    for _, child := range node.Children {
        traverseTree(child, depth+1)
    }
}

// 8. 目录遍历示例（概念性，不实际访问文件系统）
func printDirectoryStructure(path string, depth int) {
    // 基础情况：最大深度限制
    if depth > 5 {
        return
    }
    
    // 打印当前路径
    for i := 0; i < depth; i++ {
        fmt.Print("  ")
    }
    fmt.Printf("📁 %s\n", path)
    
    // 模拟子目录（实际应用中会读取真实目录）
    subdirs := []string{"subdir1", "subdir2"}
    for _, subdir := range subdirs {
        if depth < 2 { // 限制递归深度避免无限递归
            printDirectoryStructure(path+"/"+subdir, depth+1)
        }
    }
}

func main() {
    fmt.Println("=== 递归示例演示 ===\n")
    
    // 1. 阶乘
    fmt.Printf("5! = %d\n", factorial(5))
    
    // 2. 斐波那契
    fmt.Printf("第7个斐波那契数: %d\n", fibonacci(7))
    
    // 3. 二分查找
    arr := []int{1, 3, 5, 7, 9, 11, 13}
    index := binarySearch(arr, 7, 0, len(arr)-1)
    fmt.Printf("在数组中查找7的位置: %d\n", index)
    
    // 4. 数组求和
    nums := []int{1, 2, 3, 4, 5}
    fmt.Printf("数组 %v 的和: %d\n", nums, sumArray(nums))
    
    // 5. 字符串反转
    str := "Hello"
    fmt.Printf("'%s' 反转后: '%s'\n", str, reverseString(str))
    
    // 6. 幂运算
    fmt.Printf("2^8 = %d\n", power(2, 8))
    
    // 7. 树遍历
    fmt.Println("\n树结构遍历:")
    root := &TreeNode{
        Value: 1,
        Children: []*TreeNode{
            {Value: 2, Children: []*TreeNode{{Value: 4}, {Value: 5}}},
            {Value: 3, Children: []*TreeNode{{Value: 6}}},
        },
    }
    traverseTree(root, 0)
    
    // 8. 目录结构
    fmt.Println("\n目录结构示例:")
    printDirectoryStructure("root", 0)
}
